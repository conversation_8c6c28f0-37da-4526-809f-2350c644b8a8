import type { Participant, SessionParticipant } from "../../types"

interface ParticipantManagementFormProps {
  // 新增參加者狀態
  newParticipant: { name: string; category: string }
  newParticipantTitle: string
  isAddingParticipant: boolean
  allCategories: string[]

  // 編輯狀態
  editingParticipant: Participant | null
  editingSessionParticipant: SessionParticipant | null

  // 事件處理
  onNewParticipantChange: (participant: { name: string; category: string }) => void
  onNewParticipantTitleChange: (title: string) => void
  onAddParticipant: () => void
  onEditingParticipantChange: (participant: Participant | null) => void
  onEditingSessionParticipantChange: (participant: SessionParticipant | null) => void
  onUpdateParticipant: () => void
  onUpdateSessionParticipant: () => void
}

/**
 * 參加者管理表單組件
 * 用途：處理新增和編輯參加者的表單
 */
export function ParticipantManagementForm({
  newParticipant,
  newParticipantTitle,
  isAddingParticipant,
  allCategories,
  editingParticipant,
  editingSessionParticipant,
  onNewParticipantChange,
  onNewParticipantTitleChange,
  onAddParticipant,
  onEditingParticipantChange,
  onEditingSessionParticipantChange,
  onUpdateParticipant,
  onUpdateSessionParticipant,
}: ParticipantManagementFormProps) {
  return (
    <>
      {/* 新增參加者表單 */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h3 className="card-title mb-4">新增參加者</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="form-control">
              <label className="label">
                <span className="label-text">姓名</span>
              </label>
              <input
                type="text"
                value={newParticipant.name}
                onChange={(e) => onNewParticipantChange({ ...newParticipant, name: e.target.value })}
                placeholder="輸入參加者姓名"
                className="input input-bordered w-full"
              />
            </div>
            <div className="form-control">
              <label className="label">
                <span className="label-text">職銜</span>
              </label>
              <select
                value={newParticipant.category}
                onChange={(e) => onNewParticipantChange({ ...newParticipant, category: e.target.value })}
                className="select select-bordered w-full"
              >
                <option value="">選擇職銜</option>
                {allCategories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
                <option value="新增職銜">+ 新增職銜</option>
              </select>
            </div>
            {newParticipant.category === "新增職銜" && (
              <div className="form-control">
                <label className="label">
                  <span className="label-text">新職銜名稱</span>
                </label>
                <input
                  type="text"
                  value={newParticipantTitle}
                  onChange={(e) => onNewParticipantTitleChange(e.target.value)}
                  placeholder="輸入新職銜名稱"
                  className="input input-bordered w-full"
                />
              </div>
            )}
          </div>
          <div className="card-actions justify-start mt-4">
            <button
              onClick={onAddParticipant}
              disabled={!newParticipant.name.trim() || isAddingParticipant || (newParticipant.category === "新增職銜" && !newParticipantTitle.trim())}
              className="btn btn-primary"
            >
              {isAddingParticipant ? "新增中..." : "新增參加者"}
            </button>
          </div>
        </div>
      </div>

      {/* 編輯參加者對話框 */}
      {editingParticipant && (
        <dialog className="modal modal-open">
          <div className="modal-box">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">編輯參加者</h3>
              <button
                onClick={() => onEditingParticipantChange(null)}
                className="btn btn-sm btn-circle btn-ghost"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="space-y-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">姓名</span>
                </label>
                <input
                  type="text"
                  value={editingParticipant.name}
                  onChange={(e) => onEditingParticipantChange({ ...editingParticipant, name: e.target.value })}
                  className="input input-bordered w-full"
                />
              </div>
              <div className="form-control">
                <label className="label">
                  <span className="label-text">職銜</span>
                </label>
                <select
                  value={editingParticipant.category || ""}
                  onChange={(e) => onEditingParticipantChange({ ...editingParticipant, category: e.target.value })}
                  className="select select-bordered w-full"
                >
                  <option value="">選擇職銜</option>
                  {allCategories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="modal-action">
              <button
                onClick={() => onEditingParticipantChange(null)}
                className="btn btn-ghost"
              >
                取消
              </button>
              <button
                onClick={onUpdateParticipant}
                className="btn btn-primary"
              >
                更新
              </button>
            </div>
          </div>
          <form method="dialog" className="modal-backdrop">
            <button onClick={() => onEditingParticipantChange(null)}>close</button>
          </form>
        </dialog>
      )}

      {/* 編輯屆別參加者對話框 */}
      {editingSessionParticipant && (
        <dialog className="modal modal-open">
          <div className="modal-box">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">編輯屆別參加者</h3>
              <button
                onClick={() => onEditingSessionParticipantChange(null)}
                className="btn btn-sm btn-circle btn-ghost"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="space-y-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">姓名</span>
                </label>
                <input
                  type="text"
                  value={editingSessionParticipant.name}
                  onChange={(e) => onEditingSessionParticipantChange({ ...editingSessionParticipant, name: e.target.value })}
                  className="input input-bordered w-full"
                />
              </div>
              <div className="form-control">
                <label className="label">
                  <span className="label-text">職銜</span>
                </label>
                <input
                  type="text"
                  value={editingSessionParticipant.category || ""}
                  onChange={(e) => onEditingSessionParticipantChange({ ...editingSessionParticipant, category: e.target.value })}
                  className="input input-bordered w-full"
                />
              </div>
              <div className="form-control">
                <label className="label">
                  <span className="label-text">加入日期</span>
                </label>
                <input
                  type="date"
                  value={editingSessionParticipant.joinDate}
                  onChange={(e) => onEditingSessionParticipantChange({ ...editingSessionParticipant, joinDate: e.target.value })}
                  className="input input-bordered w-full"
                />
              </div>
              <div className="form-control">
                <label className="label cursor-pointer">
                  <span className="label-text">活躍狀態</span>
                  <input
                    type="checkbox"
                    checked={editingSessionParticipant.isActive}
                    onChange={(e) => onEditingSessionParticipantChange({ ...editingSessionParticipant, isActive: e.target.checked })}
                    className="toggle toggle-primary"
                  />
                </label>
              </div>
            </div>
            <div className="modal-action">
              <button
                onClick={() => onEditingSessionParticipantChange(null)}
                className="btn btn-ghost"
              >
                取消
              </button>
              <button
                onClick={onUpdateSessionParticipant}
                className="btn btn-primary"
              >
                更新
              </button>
            </div>
          </div>
          <form method="dialog" className="modal-backdrop">
            <button onClick={() => onEditingSessionParticipantChange(null)}>close</button>
          </form>
        </dialog>
      )}
    </>
  )
}
