"use client"

import { cn } from "@/lib/utils"
import { ConnectionStatus, type ConnectionState } from "../../types/google-sheets"

export type ViewMode = "dashboard" | "activities" | "statistics" | "committees" | "sessions" | "participants" | "test" | "google-sheets" | "design-test" | "nav-test"

interface FixedNavigationProps {
  viewMode: ViewMode
  onViewModeChange: (mode: ViewMode) => void
  selectedActivity: any | null
  googleSheetsConnectionState: ConnectionState
  className?: string
}

const navigationItems = [
  { key: "dashboard", label: "儀表板", icon: "🏠" },
  { key: "activities", label: "活動管理", icon: "📅" },
  { key: "participants", label: "參加者管理", icon: "👥" },
  { key: "statistics", label: "出席統計", icon: "📊" },
  { key: "committees", label: "委員會管理", icon: "🏢" },
  { key: "sessions", label: "屆別管理", icon: "📋" },
  { key: "google-sheets", label: "Google Sheets", icon: "📊" },
  { key: "test", label: "功能測試", icon: "🧪" },
  { key: "design-test", label: "設計測試", icon: "🎨" },
  { key: "nav-test", label: "導航測試", icon: "🧭" },
] as const

/**
 * Fixed Navigation Component
 * Provides a consistent, sticky navigation experience across all views
 */
export function FixedNavigation({
  viewMode,
  onViewModeChange,
  selectedActivity,
  googleSheetsConnectionState,
  className
}: FixedNavigationProps) {
  return (
    <nav
      className={cn(
        "nav-professional fixed top-0 left-0 right-0 z-40",
        "bg-base-100/95 backdrop-blur-md border-b border-base-300/50",
        "shadow-elegant transition-all duration-200",
        className
      )}
      role="navigation"
      aria-label="主要導航"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo and Title Section */}
          <div className="flex items-center space-x-3 lg:space-x-4 min-w-0 flex-1">
            <div className="flex-shrink-0">
              <h1 className="text-lg lg:text-2xl font-bold text-base-content truncate">
                <span className="hidden sm:inline">HKUYA 出席管理系統</span>
                <span className="sm:hidden">HKUYA</span>
              </h1>
            </div>

            {/* Google Sheets Status Indicator */}
            <div className="hidden md:flex items-center space-x-2 text-xs lg:text-sm">
              {googleSheetsConnectionState.status === ConnectionStatus.CONNECTED ? (
                <div className="flex items-center space-x-1 text-success">
                  <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                  <span className="hidden lg:inline">Google Sheets 已連接</span>
                  <span className="lg:hidden">已連接</span>
                </div>
              ) : googleSheetsConnectionState.status === ConnectionStatus.ERROR ? (
                <div className="flex items-center space-x-1 text-error">
                  <div className="w-2 h-2 bg-error rounded-full"></div>
                  <span className="hidden lg:inline">Google Sheets 連接錯誤</span>
                  <span className="lg:hidden">連接錯誤</span>
                </div>
              ) : googleSheetsConnectionState.status === ConnectionStatus.CONNECTING ? (
                <div className="flex items-center space-x-1 text-info">
                  <div className="w-2 h-2 bg-info rounded-full animate-pulse"></div>
                  <span className="hidden lg:inline">Google Sheets 連接中</span>
                  <span className="lg:hidden">連接中</span>
                </div>
              ) : (
                <div className="flex items-center space-x-1 text-base-content/60">
                  <div className="w-2 h-2 bg-base-content/40 rounded-full"></div>
                  <span className="hidden lg:inline">Google Sheets 未連接</span>
                  <span className="lg:hidden">未連接</span>
                </div>
              )}
            </div>
          </div>

          {/* Navigation Items */}
          {!selectedActivity && (
            <div className="flex items-center space-x-1 lg:space-x-2 overflow-x-auto scrollbar-hide">
              <div className="flex space-x-1 lg:space-x-2 min-w-max">
                {navigationItems.map(({ key, label, icon }) => (
                  <button
                    key={key}
                    onClick={() => onViewModeChange(key as ViewMode)}
                    className={cn(
                      "btn-professional btn-sm lg:btn-md",
                      "px-2 lg:px-4 py-1 lg:py-2",
                      "text-xs lg:text-sm font-medium",
                      "transition-all duration-200",
                      "whitespace-nowrap",
                      viewMode === key
                        ? "btn-primary shadow-elegant"
                        : "btn-ghost hover:btn-outline",
                    )}
                    aria-current={viewMode === key ? "page" : undefined}
                    title={label}
                  >
                    <span className="lg:hidden text-sm" aria-hidden="true">{icon}</span>
                    <span className="hidden sm:inline lg:inline">{label}</span>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Back Button for Activity View */}
          {selectedActivity && (
            <div className="flex items-center space-x-2">
              <button
                onClick={() => onViewModeChange("activities")}
                className="btn-professional btn-ghost btn-sm lg:btn-md gap-2"
                aria-label="返回活動列表"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                <span className="hidden sm:inline">返回</span>
              </button>
              <div className="text-sm lg:text-base font-medium text-base-content/80 truncate max-w-xs">
                {selectedActivity.name}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Navigation Overflow Indicator */}
      <div className="lg:hidden absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-base-100 to-transparent pointer-events-none" />
    </nav>
  )
}

/**
 * Navigation Spacer Component
 * Provides proper spacing for content below the fixed navigation
 */
export function NavigationSpacer({ className }: { className?: string }) {
  return (
    <div
      className={cn("h-16 lg:h-20", className)}
      aria-hidden="true"
    />
  )
}
