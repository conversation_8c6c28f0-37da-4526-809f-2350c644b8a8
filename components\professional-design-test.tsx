"use client"

import { useState } from "react"

/**
 * Professional Design System Test Component
 * Tests all professional CSS classes and interactive components
 */
export function ProfessionalDesignTest() {
  const [showModal, setShowModal] = useState(false)
  const [currentTheme, setCurrentTheme] = useState("professional")
  const [formData, setFormData] = useState({
    name: "",
    category: "",
    message: ""
  })

  const handleThemeChange = (theme: string) => {
    setCurrentTheme(theme)
    document.documentElement.setAttribute('data-theme', theme)
  }

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    alert(`Form submitted with: ${JSON.stringify(formData, null, 2)}`)
  }

  return (
    <div className="space-y-8 p-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-base-content mb-2">
          Professional Design System Test
        </h1>
        <p className="text-base-content text-opacity-70">
          Testing DaisyUI 5 integration with professional styling
        </p>
      </div>

      {/* Theme Switcher */}
      <div className="card-professional">
        <div className="card-body">
          <h2 className="card-title">Theme Switching Test</h2>
          <p className="text-base-content text-opacity-80 mb-4">
            Current theme: <span className="font-semibold">{currentTheme}</span>
          </p>
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => handleThemeChange("professional")}
              className={`btn-professional ${currentTheme === "professional" ? "btn-primary" : "btn-outline"}`}
            >
              Professional Light
            </button>
            <button
              onClick={() => handleThemeChange("professional-dark")}
              className={`btn-professional ${currentTheme === "professional-dark" ? "btn-primary" : "btn-outline"}`}
            >
              Professional Dark
            </button>
            <button
              onClick={() => handleThemeChange("light")}
              className={`btn-professional ${currentTheme === "light" ? "btn-primary" : "btn-outline"}`}
            >
              DaisyUI Light
            </button>
            <button
              onClick={() => handleThemeChange("dark")}
              className={`btn-professional ${currentTheme === "dark" ? "btn-primary" : "btn-outline"}`}
            >
              DaisyUI Dark
            </button>
          </div>
        </div>
      </div>

      {/* Button Tests */}
      <div className="card-professional">
        <div className="card-body">
          <h2 className="card-title">Professional Buttons Test</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button className="btn-professional btn-primary">Primary</button>
            <button className="btn-professional btn-secondary">Secondary</button>
            <button className="btn-professional btn-accent">Accent</button>
            <button className="btn-professional btn-success">Success</button>
            <button className="btn-professional btn-warning">Warning</button>
            <button className="btn-professional btn-error">Error</button>
            <button className="btn-professional btn-info">Info</button>
            <button className="btn-professional btn-ghost">Ghost</button>
          </div>
        </div>
      </div>

      {/* Form Controls Test */}
      <div className="card-professional">
        <div className="card-body">
          <h2 className="card-title">Professional Form Controls Test</h2>
          <form onSubmit={handleFormSubmit} className="space-y-4">
            <div className="form-control">
              <label className="label">
                <span className="label-text font-medium">Name</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                placeholder="Enter your name"
                className="input-professional"
              />
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text font-medium">Category</span>
              </label>
              <select
                value={formData.category}
                onChange={(e) => setFormData({...formData, category: e.target.value})}
                className="select-professional"
              >
                <option value="">Select category</option>
                <option value="member">Member</option>
                <option value="committee">Committee</option>
                <option value="executive">Executive</option>
              </select>
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text font-medium">Message</span>
              </label>
              <textarea
                value={formData.message}
                onChange={(e) => setFormData({...formData, message: e.target.value})}
                placeholder="Enter your message"
                className="textarea-professional"
                rows={3}
              />
            </div>

            <div className="flex gap-2">
              <button type="submit" className="btn-professional btn-primary">
                Submit Form
              </button>
              <button
                type="button"
                onClick={() => setShowModal(true)}
                className="btn-professional btn-secondary"
              >
                Open Modal
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Status Indicators Test */}
      <div className="card-professional">
        <div className="card-body">
          <h2 className="card-title">Status Indicators Test</h2>
          <div className="flex flex-wrap gap-2">
            <span className="status-success">Success Status</span>
            <span className="status-warning">Warning Status</span>
            <span className="status-error">Error Status</span>
            <span className="status-info">Info Status</span>
          </div>
        </div>
      </div>

      {/* Table Test */}
      <div className="card-professional">
        <div className="card-body">
          <h2 className="card-title">Professional Table Test</h2>
          <div className="overflow-x-auto">
            <table className="table-professional w-full">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Role</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>John Doe</td>
                  <td>Member</td>
                  <td><span className="status-success">Active</span></td>
                  <td>
                    <button className="btn-professional btn-ghost btn-xs">Edit</button>
                  </td>
                </tr>
                <tr>
                  <td>Jane Smith</td>
                  <td>Committee</td>
                  <td><span className="status-warning">Pending</span></td>
                  <td>
                    <button className="btn-professional btn-ghost btn-xs">Edit</button>
                  </td>
                </tr>
                <tr>
                  <td>Bob Johnson</td>
                  <td>Executive</td>
                  <td><span className="status-error">Inactive</span></td>
                  <td>
                    <button className="btn-professional btn-ghost btn-xs">Edit</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Progress and Divider Test */}
      <div className="card-professional">
        <div className="card-body">
          <h2 className="card-title">Progress & Divider Test</h2>
          <div className="space-y-4">
            <div>
              <label className="label">
                <span className="label-text">Progress Example</span>
              </label>
              <progress className="progress-professional progress-primary w-full" value="70" max="100"></progress>
            </div>

            <div className="divider-professional">Professional Divider</div>

            <div>
              <label className="label">
                <span className="label-text">Another Progress</span>
              </label>
              <progress className="progress-professional progress-success w-full" value="45" max="100"></progress>
            </div>
          </div>
        </div>
      </div>

      {/* Responsive Design Test */}
      <div className="card-professional">
        <div className="card-body">
          <h2 className="card-title">Responsive Design Test</h2>
          <div className="space-y-4">
            <div className="alert alert-info">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>Resize your browser window to test responsive behavior</span>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              <div className="card bg-primary text-primary-content">
                <div className="card-body p-4">
                  <h3 className="card-title text-sm">Mobile First</h3>
                  <p className="text-xs">Always visible</p>
                </div>
              </div>
              <div className="card bg-secondary text-secondary-content">
                <div className="card-body p-4">
                  <h3 className="card-title text-sm">SM+ (640px)</h3>
                  <p className="text-xs">2 columns on small screens</p>
                </div>
              </div>
              <div className="card bg-accent text-accent-content">
                <div className="card-body p-4">
                  <h3 className="card-title text-sm">LG+ (1024px)</h3>
                  <p className="text-xs">3 columns on large screens</p>
                </div>
              </div>
              <div className="card bg-info text-info-content">
                <div className="card-body p-4">
                  <h3 className="card-title text-sm">XL+ (1280px)</h3>
                  <p className="text-xs">4 columns on extra large</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Animation Test */}
      <div className="card-professional">
        <div className="card-body">
          <h2 className="card-title">Animation Test</h2>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-4">
              <div className="animate-fadeIn bg-primary text-primary-content p-4 rounded-lg">
                <span className="text-sm font-medium">Fade In Animation</span>
              </div>
              <div className="animate-slideUp bg-secondary text-secondary-content p-4 rounded-lg" style={{animationDelay: '0.2s'}}>
                <span className="text-sm font-medium">Slide Up Animation</span>
              </div>
              <div className="animate-scaleIn bg-accent text-accent-content p-4 rounded-lg" style={{animationDelay: '0.4s'}}>
                <span className="text-sm font-medium">Scale In Animation</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modal Test */}
      {showModal && (
        <dialog className="modal modal-open modal-professional">
          <div className="modal-box">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Professional Modal Test</h3>
              <button
                onClick={() => setShowModal(false)}
                className="btn-professional btn-sm btn-circle btn-ghost"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="space-y-4">
              <p>This modal tests the professional styling with:</p>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Professional modal classes</li>
                <li>Elegant shadows and animations</li>
                <li>Proper spacing and typography</li>
                <li>Smooth scale-in animation</li>
              </ul>
            </div>
            <div className="modal-action">
              <button
                onClick={() => setShowModal(false)}
                className="btn-professional btn-ghost"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowModal(false)}
                className="btn-professional btn-primary"
              >
                Confirm
              </button>
            </div>
          </div>
          <form method="dialog" className="modal-backdrop">
            <button onClick={() => setShowModal(false)}>close</button>
          </form>
        </dialog>
      )}
    </div>
  )
}
