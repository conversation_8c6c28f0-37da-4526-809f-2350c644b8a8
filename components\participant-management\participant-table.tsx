"use client"

import { cn } from "@/lib/utils"
import type { SessionParticipant } from "../../types"
import { getActivityLevelText, getActivityLevelColor } from "../../utils/activity-level"

interface ParticipantWithStats extends SessionParticipant {
  attendanceRate: number
  activityLevel: string
  totalParticipated: number
  totalAttended: number
}

interface ParticipantTableProps {
  participants: ParticipantWithStats[]
  sortField: string
  sortDirection: "asc" | "desc"
  onSort: (field: string) => void
  onEdit: (participant: SessionParticipant) => void
  onRemove: (participantId: string) => void
  onViewHistory: (participant: any) => void
}

export function ParticipantTable({
  participants,
  sortField,
  sortDirection,
  onSort,
  onEdit,
  onRemove,
  onViewHistory,
}: ParticipantTableProps) {
  const getSortIcon = (field: string) => {
    if (sortField !== field) {
      return (
        <svg className="w-4 h-4 ml-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
          />
        </svg>
      )
    }
    return sortDirection === "asc" ? (
      <svg className="w-4 h-4 ml-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
      </svg>
    ) : (
      <svg className="w-4 h-4 ml-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
      </svg>
    )
  }

  return (
    <div className="card bg-base-100 shadow-xl overflow-hidden">
      <div className="overflow-x-auto">
        <table className="table table-zebra">
          <thead>
            <tr>
              <th
                className="cursor-pointer hover:bg-base-200 transition-colors"
                onClick={() => onSort("name")}
              >
                <div className="flex items-center">
                  姓名
                  {getSortIcon("name")}
                </div>
              </th>
              <th
                className="cursor-pointer hover:bg-base-200 transition-colors"
                onClick={() => onSort("category")}
              >
                <div className="flex items-center">
                  職銜
                  {getSortIcon("category")}
                </div>
              </th>
              <th
                className="cursor-pointer hover:bg-base-200 transition-colors"
                onClick={() => onSort("joinDate")}
              >
                <div className="flex items-center">
                  加入日期
                  {getSortIcon("joinDate")}
                </div>
              </th>
              <th
                className="cursor-pointer hover:bg-base-200 transition-colors"
                onClick={() => onSort("status")}
              >
                <div className="flex items-center">
                  狀態
                  {getSortIcon("status")}
                </div>
              </th>
              <th
                className="cursor-pointer hover:bg-base-200 transition-colors"
                onClick={() => onSort("attendanceRate")}
              >
                <div className="flex items-center">
                  出席率
                  {getSortIcon("attendanceRate")}
                </div>
              </th>
              <th>活躍等級</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {participants.length > 0 ? (
              participants.map((participant) => (
                <tr key={participant.id} className="hover:bg-base-200">
                  <td>{participant.name}</td>
                  <td>{participant.category || "未設定"}</td>
                  <td>{participant.joinDate || "未設定"}</td>
                  <td>
                    <div
                      className={cn(
                        "badge",
                        participant.isActive ? "badge-success" : "badge-error"
                      )}
                    >
                      {participant.isActive ? "活躍" : "非活躍"}
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center">
                      <span className="mr-2">{participant.attendanceRate.toFixed(1)}%</span>
                      <progress
                        className={cn(
                          "progress w-16",
                          participant.attendanceRate >= 80
                            ? "progress-success"
                            : participant.attendanceRate >= 60
                              ? "progress-warning"
                              : "progress-error"
                        )}
                        value={participant.attendanceRate}
                        max="100"
                      />
                    </div>
                    <div className="text-xs opacity-60 mt-1">
                      {participant.totalAttended}/{participant.totalParticipated} 次
                    </div>
                  </td>
                  <td>
                    <div
                      className={cn(
                        "badge",
                        getActivityLevelColor(participant.activityLevel),
                      )}
                    >
                      {getActivityLevelText(participant.activityLevel)}
                    </div>
                  </td>
                  <td>
                    <div className="flex gap-2">
                      <button
                        onClick={() => onViewHistory(participant)}
                        className="btn btn-ghost btn-xs"
                      >
                        歷史
                      </button>
                      <button
                        onClick={() => onEdit(participant)}
                        className="btn btn-ghost btn-xs"
                      >
                        編輯
                      </button>
                      <button
                        onClick={() => {
                          if (window.confirm(`確定要從當前屆別移除 "${participant.name}" 嗎？`)) {
                            onRemove(participant.id)
                          }
                        }}
                        className="btn btn-ghost btn-xs text-error"
                      >
                        移除
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="text-center py-8 text-base-content opacity-60">
                  沒有找到符合條件的參加者
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}
