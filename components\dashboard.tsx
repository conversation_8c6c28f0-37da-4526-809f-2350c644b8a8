"use client"

import { useState } from "react"
import type { Activity, Participant, Session, ActivityLevelSettings } from "../types"
import { calculateStatistics } from "../utils/statistics"
import { ExportDialog } from "./export-dialog"
import { KPICards } from "./dashboard/kpi-cards"
import { SmartInsights } from "./dashboard/smart-insights"
import { TrendCharts } from "./dashboard/trend-charts"
import { useDashboardData } from "../hooks/use-dashboard-data"
import { DEFAULT_ACTIVITY_LEVEL_SETTINGS } from "../utils/activity-level"

interface DashboardProps {
  activities: Activity[]
  allParticipants: Participant[]
  sessions?: Session[]
  onViewActivities: () => void
  onViewStatistics: () => void
  onAddActivity: () => void
  activityLevelSettings?: ActivityLevelSettings
  onUpdateActivityLevelSettings?: (settings: ActivityLevelSettings) => void
}

export function Dashboard({
  activities,
  allParticipants,
  sessions = [],
  onViewActivities,
  onViewStatistics,
  onAddActivity,
  activityLevelSettings: propActivityLevelSettings,
  onUpdateActivityLevelSettings: propOnUpdateActivityLevelSettings,
}: DashboardProps) {
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [activityLevelSettings] = useState<ActivityLevelSettings>(
    propActivityLevelSettings || DEFAULT_ACTIVITY_LEVEL_SETTINGS,
  )

  // 使用自定義 Hook 獲取儀表板數據
  const { kpis, monthlyData, weeklyAttendance, insights } = useDashboardData({
    activities,
    allParticipants,
    sessions,
  })

  // 計算統計數據
  const statistics = calculateStatistics(activities, allParticipants)



  return (
    <div className="space-y-6">
      {/* KPI 卡片 */}
      <KPICards
        kpis={kpis}
        averageAttendanceRate={statistics.averageAttendanceRate}
        allParticipantsCount={allParticipants.length}
      />

      {/* 智能洞察 */}
      <SmartInsights insights={insights} />

      {/* 趨勢圖表 */}
      <TrendCharts
        monthlyData={monthlyData}
        weeklyAttendance={weeklyAttendance}
      />



      {/* 快速操作 */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h3 className="card-title text-base-content mb-4">快速操作</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={onAddActivity}
              className="btn btn-primary btn-lg"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              新增活動
            </button>
            <button
              onClick={onViewStatistics}
              className="btn btn-accent btn-lg"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              詳細統計
            </button>
            <button
              onClick={() => setShowExportDialog(true)}
              className="btn btn-success btn-lg"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              導出數據
            </button>
          </div>
        </div>
      </div>

      {/* 導出對話框 */}
      {showExportDialog && (
        <ExportDialog
          activities={activities}
          allParticipants={allParticipants}
          onClose={() => setShowExportDialog(false)}
        />
      )}
    </div>
  )
}
