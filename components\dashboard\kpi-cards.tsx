"use client"

import { cn } from "@/lib/utils"

interface KPICardsProps {
  kpis: {
    thisMonthActivities: number
    lastMonthActivities: number
    thisMonthAvgAttendance: number
    attendanceChange: number
    engagementRate: number
    activeParticipants: number
  }
  averageAttendanceRate: number
  allParticipantsCount: number
}

export function KPICards({ kpis, averageAttendanceRate, allParticipantsCount }: KPICardsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div className="card bg-primary text-primary-content shadow-xl">
        <div className="card-body">
          <h3 className="text-sm font-medium opacity-90">本月活動數</h3>
          <div className="flex items-end justify-between">
            <p className="text-2xl font-bold">{kpis.thisMonthActivities}</p>
            <div className="text-right">
              <p className="text-xs opacity-75">上月: {kpis.lastMonthActivities}</p>
              <p className={cn(
                "text-xs font-medium",
                kpis.thisMonthActivities >= kpis.lastMonthActivities ? "text-success" : "text-error"
              )}>
                {kpis.thisMonthActivities >= kpis.lastMonthActivities ? "↗" : "↘"}
                {Math.abs(kpis.thisMonthActivities - kpis.lastMonthActivities)}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="card bg-success text-success-content shadow-xl">
        <div className="card-body">
          <h3 className="text-sm font-medium opacity-90">本月平均出席率</h3>
          <div className="flex items-end justify-between">
            <p className="text-2xl font-bold">{kpis.thisMonthAvgAttendance}%</p>
            <div className="text-right">
              <p className={cn(
                "text-xs font-medium",
                kpis.attendanceChange >= 0 ? "text-success-content" : "text-error"
              )}>
                {kpis.attendanceChange >= 0 ? "↗" : "↘"} {Math.abs(kpis.attendanceChange).toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="card bg-accent text-accent-content shadow-xl">
        <div className="card-body">
          <h3 className="text-sm font-medium opacity-90">參與度</h3>
          <div className="flex items-end justify-between">
            <p className="text-2xl font-bold">{kpis.engagementRate}%</p>
            <div className="text-right">
              <p className="text-xs opacity-75">{kpis.activeParticipants}/{allParticipantsCount}</p>
              <p className="text-xs font-medium opacity-75">活躍參與者</p>
            </div>
          </div>
        </div>
      </div>

      <div className="card bg-warning text-warning-content shadow-xl">
        <div className="card-body">
          <h3 className="text-sm font-medium opacity-90">整體出席率</h3>
          <div className="flex items-end justify-between">
            <p className="text-2xl font-bold">{averageAttendanceRate.toFixed(1)}%</p>
            <div className="text-right">
              <p className="text-xs opacity-75">目標: 80%</p>
              <p className={cn(
                "text-xs font-medium",
                averageAttendanceRate >= 80 ? "text-success" : "text-warning-content"
              )}>
                {averageAttendanceRate >= 80 ? "達標" : "待改善"}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
