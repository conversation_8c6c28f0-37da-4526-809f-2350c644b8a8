"use client"

import { useState } from "react"
import { cn } from "@/lib/utils"
import { ActivityManagement } from "./components/activity-management"
import { ParticipantManagement } from "./components/participant-management"
import { ParticipantManagementPage } from "./components/participant-management-page"
import { Dashboard } from "./components/dashboard"
import { StatisticsDashboard } from "./components/statistics-dashboard"
import TestStatisticsDashboard from "./components/test-statistics-dashboard"
import { CommitteeManagement } from "./components/committee-management"
import { SessionManagement } from "./components/session-management"
import { GoogleSheetsStatus } from "./components/google-sheets-status"
import { GoogleSheetsSettings } from "./components/google-sheets-settings"
import { ProfessionalDesignTest } from "./components/professional-design-test"
import { useAttendanceData } from "./hooks/use-attendance-data"
import { useActivityHandlers } from "./hooks/use-activity-handlers"
import { useParticipantHandlers } from "./hooks/use-participant-handlers"
import { useCommitteeHandlers } from "./hooks/use-committee-handlers"
import { useSessionHandlers } from "./hooks/use-session-handlers"
import { useGoogleSheets } from "./hooks/use-google-sheets"
import type { ActivityLevelSettings } from "./types"
import type { GoogleSheetsConfig, AttendanceRecord } from "./types/google-sheets"
import { DEFAULT_ACTIVITY_LEVEL_SETTINGS } from "./utils/activity-level"

interface AttendanceTrackerProps {
  className?: string
}

type ViewMode = "dashboard" | "activities" | "statistics" | "committees" | "sessions" | "participants" | "test" | "google-sheets" | "design-test"

export function AttendanceTracker({ className }: AttendanceTrackerProps) {
  const [selectedActivityId, setSelectedActivityId] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<ViewMode>("dashboard")
  const [activityLevelSettings, setActivityLevelSettings] = useState<ActivityLevelSettings>(
    DEFAULT_ACTIVITY_LEVEL_SETTINGS,
  )

  // Google Sheets 配置狀態
  const [googleSheetsConfig, setGoogleSheetsConfig] = useState<GoogleSheetsConfig>({
    spreadsheetId: process.env.NEXT_PUBLIC_GOOGLE_SPREADSHEET_ID || '',
    serviceAccountEmail: process.env.NEXT_PUBLIC_GOOGLE_SERVICE_ACCOUNT_EMAIL,
    privateKey: process.env.NEXT_PUBLIC_GOOGLE_PRIVATE_KEY,
    projectId: process.env.NEXT_PUBLIC_GOOGLE_PROJECT_ID,
    clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
    clientSecret: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET
  })

  // OAuth2 access token 狀態
  const [accessToken, setAccessToken] = useState<string | null>(null)

  const {
    activities,
    allParticipants,
    sessionParticipants,
    committees,
    sessions,
    selectedSessionId,
    setActivities,
    setAllParticipants,
    setSessionParticipants,
    setCommittees,
    setSessions,
    setSelectedSessionId,
    selectedActivity,
    currentSessionCommittees,
    currentSessionActivities,
  } = useAttendanceData(selectedActivityId)

  const activityHandlers = useActivityHandlers({
    activities,
    allParticipants,
    sessionParticipants,
    sessions,
    selectedSessionId,
    setActivities,
    setAllParticipants,
    setSessionParticipants,
  })

  const participantHandlers = useParticipantHandlers({
    allParticipants,
    sessionParticipants,
    activities,
    setAllParticipants,
    setSessionParticipants,
    setActivities,
  })

  const committeeHandlers = useCommitteeHandlers({
    committees,
    activities,
    setCommittees,
    setActivities,
  })

  const sessionHandlers = useSessionHandlers({
    sessions,
    activities,
    sessionParticipants,
    selectedSessionId,
    setSessions,
    setActivities,
    setSessionParticipants,
    setSelectedSessionId,
  })

  // Google Sheets 整合
  const googleSheets = useGoogleSheets({
    config: googleSheetsConfig,
    autoSync: false, // 預設關閉自動同步
    syncInterval: 5 * 60 * 1000, // 5分鐘
    accessToken: accessToken || undefined
  })

  const handleBackToMain = () => {
    setSelectedActivityId(null)
    setViewMode("dashboard")
  }

  // Google Sheets 處理函數
  const handleGoogleSheetsSync = async (): Promise<void> => {
    // 準備出席記錄數據
    const attendanceRecords: AttendanceRecord[] = []

    activities.forEach(activity => {
      activity.participants.forEach(participant => {
        const attendanceStatus = participant.attendance?.[activity.id]
        if (attendanceStatus) {
          // 處理 boolean 或 AttendanceStatus 類型
          if (typeof attendanceStatus === 'boolean') {
            attendanceRecords.push({
              participantId: participant.id,
              participantName: participant.name,
              activityId: activity.id,
              activityName: activity.name,
              status: attendanceStatus ? 'present' : 'absent'
            })
          } else {
            attendanceRecords.push({
              participantId: participant.id,
              participantName: participant.name,
              activityId: activity.id,
              activityName: activity.name,
              status: attendanceStatus.status,
              checkInTime: attendanceStatus.checkInTime,
              notes: attendanceStatus.notes,
              recordedBy: attendanceStatus.recordedBy
            })
          }
        }
      })
    })

    // 執行同步
    await googleSheets.syncToSheets({
      participants: allParticipants,
      activities,
      sessions,
      attendanceRecords
    })
  }

  const handleGoogleSheetsConnect = async () => {
    return await googleSheets.connect()
  }

  const handleGoogleSheetsDisconnect = () => {
    googleSheets.disconnect()
  }

  const handleToggleAutoSync = () => {
    if (googleSheets.syncStatus.autoSyncEnabled) {
      googleSheets.disableAutoSync()
    } else {
      googleSheets.enableAutoSync()
    }
  }

  const renderContent = () => {
    if (selectedActivity) {
      return (
        <ParticipantManagement
          activity={selectedActivity}
          allParticipants={allParticipants}
          onAddParticipant={activityHandlers.handleAddParticipantToActivity}
          onRemoveParticipant={activityHandlers.handleRemoveParticipantFromActivity}
          onUpdateParticipant={activityHandlers.handleUpdateParticipantInActivity}
          onToggleAttendance={activityHandlers.handleToggleAttendance}
          onBulkAddParticipants={activityHandlers.handleBulkAddParticipantsToActivity}
          onBack={handleBackToMain}
        />
      )
    }

    switch (viewMode) {
      case "dashboard":
        return (
          <Dashboard
            activities={currentSessionActivities}
            allParticipants={allParticipants}
            sessions={sessions}
            onViewActivities={() => setViewMode("activities")}
            onViewStatistics={() => setViewMode("statistics")}
            onAddActivity={() => setViewMode("activities")}
            activityLevelSettings={activityLevelSettings}
            onUpdateActivityLevelSettings={setActivityLevelSettings}
          />
        )

      case "participants":
        return (
          <ParticipantManagementPage
            allParticipants={allParticipants}
            sessionParticipants={sessionParticipants}
            sessions={sessions}
            selectedSessionId={selectedSessionId}
            activities={activities}
            onAddParticipant={participantHandlers.handleAddParticipant}
            onAddSessionParticipant={participantHandlers.handleAddSessionParticipant}
            onBulkAddSessionParticipants={participantHandlers.handleBulkAddSessionParticipants}
            onBulkAddParticipants={participantHandlers.handleBulkAddParticipants}
            onUpdateParticipant={participantHandlers.handleUpdateParticipant}
            onUpdateSessionParticipant={participantHandlers.handleUpdateSessionParticipant}
            onDeleteParticipant={participantHandlers.handleDeleteParticipant}
            onRemoveFromSession={participantHandlers.handleRemoveFromSession}
            onBulkDeleteTitle={participantHandlers.handleBulkDeleteTitle}
            onBack={handleBackToMain}
            activityLevelSettings={activityLevelSettings}
          />
        )

      case "statistics":
        return (
          <StatisticsDashboard
            activities={currentSessionActivities}
            allParticipants={allParticipants}
            sessions={sessions}
            onBack={handleBackToMain}
          />
        )

      case "committees":
        return (
          <CommitteeManagement
            committees={committees}
            sessions={sessions}
            selectedSessionId={selectedSessionId}
            onAddCommittee={committeeHandlers.handleAddCommittee}
            onBulkAddCommittees={committeeHandlers.handleBulkAddCommittees}
            onEditCommittee={committeeHandlers.handleEditCommittee}
            onDeleteCommittee={committeeHandlers.handleDeleteCommittee}
            onBack={handleBackToMain}
          />
        )

      case "sessions":
        return (
          <SessionManagement
            sessions={sessions}
            onAddSession={sessionHandlers.handleAddSession}
            onEditSession={sessionHandlers.handleEditSession}
            onDeleteSession={sessionHandlers.handleDeleteSession}
            onSetActiveSession={sessionHandlers.handleSetActiveSession}
            onBack={handleBackToMain}
          />
        )

      case "test":
        return <TestStatisticsDashboard />

      case "design-test":
        return <ProfessionalDesignTest />

      case "google-sheets":
        return (
          <div className="space-y-6">
            <GoogleSheetsStatus
              connectionState={googleSheets.connectionState}
              syncStatus={googleSheets.syncStatus}
              onConnect={handleGoogleSheetsConnect}
              onDisconnect={handleGoogleSheetsDisconnect}
              onSync={handleGoogleSheetsSync}
              onToggleAutoSync={handleToggleAutoSync}
            />

            <GoogleSheetsSettings
              config={googleSheetsConfig}
              onConfigChange={setGoogleSheetsConfig}
              onTestConnection={googleSheets.testConnection}
              onAccessTokenChange={setAccessToken}
              accessToken={accessToken}
            />
          </div>
        )

      default:
        return (
          <ActivityManagement
            activities={currentSessionActivities}
            committees={currentSessionCommittees}
            sessions={sessions}
            selectedSessionId={selectedSessionId}
            onAddActivity={activityHandlers.handleAddActivity}
            onBulkAddActivitiesWithParticipants={activityHandlers.handleBulkAddActivitiesWithParticipants}
            onSelectActivity={setSelectedActivityId}
            onDeleteActivity={activityHandlers.handleDeleteActivity}
            onEditActivity={activityHandlers.handleEditActivity}
            onViewStatistics={() => setViewMode("statistics")}
            onManageCommittees={() => setViewMode("committees")}
          />
        )
    }
  }

  return (
    <div className={cn("p-4 bg-white dark:bg-gray-800 rounded-lg shadow", className)}>
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-4">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">活動出席管理系統</h2>

          {/* Google Sheets 連接狀態指示器 */}
          <div className="flex items-center space-x-2 text-sm">
            {googleSheets.connectionState.status === 'connected' ? (
              <div className="flex items-center space-x-1 text-green-600 dark:text-green-400">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Google Sheets 已連接</span>
              </div>
            ) : googleSheets.connectionState.status === 'error' ? (
              <div className="flex items-center space-x-1 text-red-600 dark:text-red-400">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span>Google Sheets 連接錯誤</span>
              </div>
            ) : (
              <div className="flex items-center space-x-1 text-gray-500 dark:text-gray-400">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span>Google Sheets 未連接</span>
              </div>
            )}
          </div>
        </div>

        {!selectedActivity && (
          <div className="flex space-x-2">
            {[
              { key: "dashboard", label: "儀表板" },
              { key: "activities", label: "活動管理" },
              { key: "participants", label: "參加者管理" },
              { key: "statistics", label: "出席統計" },
              { key: "committees", label: "委員會管理" },
              { key: "sessions", label: "屆別管理" },
              { key: "google-sheets", label: "Google Sheets" },
              { key: "test", label: "功能測試" },
              { key: "design-test", label: "設計測試" },
            ].map(({ key, label }) => (
              <button
                key={key}
                onClick={() => setViewMode(key as ViewMode)}
                className={cn(
                  "px-4 py-2 rounded-md transition-colors",
                  viewMode === key
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500",
                )}
              >
                {label}
              </button>
            ))}
          </div>
        )}
      </div>

      {renderContent()}
    </div>
  )
}
