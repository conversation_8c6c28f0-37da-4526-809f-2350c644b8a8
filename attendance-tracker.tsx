"use client"

import { useState } from "react"
import { cn } from "@/lib/utils"
import { ActivityManagement } from "./components/activity-management"
import { ParticipantManagement } from "./components/participant-management"
import { ParticipantManagementPage } from "./components/participant-management-page"
import { Dashboard } from "./components/dashboard"
import { StatisticsDashboard } from "./components/statistics-dashboard"
import TestStatisticsDashboard from "./components/test-statistics-dashboard"
import { CommitteeManagement } from "./components/committee-management"
import { SessionManagement } from "./components/session-management"
import { GoogleSheetsStatus } from "./components/google-sheets-status"
import { GoogleSheetsSettings } from "./components/google-sheets-settings"
import { ProfessionalDesignTest } from "./components/professional-design-test"
import { NavigationTest } from "./components/navigation/navigation-test"
import { FixedNavigation, NavigationSpacer, type ViewMode as NavViewMode } from "./components/navigation/fixed-navigation"
import { useAttendanceData } from "./hooks/use-attendance-data"
import { useActivityHandlers } from "./hooks/use-activity-handlers"
import { useParticipantHandlers } from "./hooks/use-participant-handlers"
import { useCommitteeHandlers } from "./hooks/use-committee-handlers"
import { useSessionHandlers } from "./hooks/use-session-handlers"
import { useGoogleSheets } from "./hooks/use-google-sheets"
import type { ActivityLevelSettings } from "./types"
import type { GoogleSheetsConfig, AttendanceRecord } from "./types/google-sheets"
import { DEFAULT_ACTIVITY_LEVEL_SETTINGS } from "./utils/activity-level"

interface AttendanceTrackerProps {
  className?: string
}

type ViewMode = NavViewMode

export function AttendanceTracker({ className }: AttendanceTrackerProps) {
  const [selectedActivityId, setSelectedActivityId] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<ViewMode>("dashboard")
  const [activityLevelSettings, setActivityLevelSettings] = useState<ActivityLevelSettings>(
    DEFAULT_ACTIVITY_LEVEL_SETTINGS,
  )

  // Google Sheets 配置狀態
  const [googleSheetsConfig, setGoogleSheetsConfig] = useState<GoogleSheetsConfig>({
    spreadsheetId: process.env.NEXT_PUBLIC_GOOGLE_SPREADSHEET_ID || '',
    serviceAccountEmail: process.env.NEXT_PUBLIC_GOOGLE_SERVICE_ACCOUNT_EMAIL,
    privateKey: process.env.NEXT_PUBLIC_GOOGLE_PRIVATE_KEY,
    projectId: process.env.NEXT_PUBLIC_GOOGLE_PROJECT_ID,
    clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
    clientSecret: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET
  })

  // OAuth2 access token 狀態
  const [accessToken, setAccessToken] = useState<string | null>(null)

  const {
    activities,
    allParticipants,
    sessionParticipants,
    committees,
    sessions,
    selectedSessionId,
    setActivities,
    setAllParticipants,
    setSessionParticipants,
    setCommittees,
    setSessions,
    setSelectedSessionId,
    selectedActivity,
    currentSessionCommittees,
    currentSessionActivities,
  } = useAttendanceData(selectedActivityId)

  const activityHandlers = useActivityHandlers({
    activities,
    allParticipants,
    sessionParticipants,
    sessions,
    selectedSessionId,
    setActivities,
    setAllParticipants,
    setSessionParticipants,
  })

  const participantHandlers = useParticipantHandlers({
    allParticipants,
    sessionParticipants,
    activities,
    setAllParticipants,
    setSessionParticipants,
    setActivities,
  })

  const committeeHandlers = useCommitteeHandlers({
    committees,
    activities,
    setCommittees,
    setActivities,
  })

  const sessionHandlers = useSessionHandlers({
    sessions,
    activities,
    sessionParticipants,
    selectedSessionId,
    setSessions,
    setActivities,
    setSessionParticipants,
    setSelectedSessionId,
  })

  // Google Sheets 整合
  const googleSheets = useGoogleSheets({
    config: googleSheetsConfig,
    autoSync: false, // 預設關閉自動同步
    syncInterval: 5 * 60 * 1000, // 5分鐘
    accessToken: accessToken || undefined
  })

  const handleBackToMain = () => {
    setSelectedActivityId(null)
    setViewMode("dashboard")
  }

  // Google Sheets 處理函數
  const handleGoogleSheetsSync = async (): Promise<void> => {
    // 準備出席記錄數據
    const attendanceRecords: AttendanceRecord[] = []

    activities.forEach(activity => {
      activity.participants.forEach(participant => {
        const attendanceStatus = participant.attendance?.[activity.id]
        if (attendanceStatus) {
          // 處理 boolean 或 AttendanceStatus 類型
          if (typeof attendanceStatus === 'boolean') {
            attendanceRecords.push({
              participantId: participant.id,
              participantName: participant.name,
              activityId: activity.id,
              activityName: activity.name,
              status: attendanceStatus ? 'present' : 'absent'
            })
          } else {
            attendanceRecords.push({
              participantId: participant.id,
              participantName: participant.name,
              activityId: activity.id,
              activityName: activity.name,
              status: attendanceStatus.status,
              checkInTime: attendanceStatus.checkInTime,
              notes: attendanceStatus.notes,
              recordedBy: attendanceStatus.recordedBy
            })
          }
        }
      })
    })

    // 執行同步
    await googleSheets.syncToSheets({
      participants: allParticipants,
      activities,
      sessions,
      attendanceRecords
    })
  }

  const handleGoogleSheetsConnect = async () => {
    return await googleSheets.connect()
  }

  const handleGoogleSheetsDisconnect = () => {
    googleSheets.disconnect()
  }

  const handleToggleAutoSync = () => {
    if (googleSheets.syncStatus.autoSyncEnabled) {
      googleSheets.disableAutoSync()
    } else {
      googleSheets.enableAutoSync()
    }
  }

  const renderContent = () => {
    if (selectedActivity) {
      return (
        <ParticipantManagement
          activity={selectedActivity}
          allParticipants={allParticipants}
          onAddParticipant={activityHandlers.handleAddParticipantToActivity}
          onRemoveParticipant={activityHandlers.handleRemoveParticipantFromActivity}
          onUpdateParticipant={activityHandlers.handleUpdateParticipantInActivity}
          onToggleAttendance={activityHandlers.handleToggleAttendance}
          onBulkAddParticipants={activityHandlers.handleBulkAddParticipantsToActivity}
          onBack={handleBackToMain}
        />
      )
    }

    switch (viewMode) {
      case "dashboard":
        return (
          <Dashboard
            activities={currentSessionActivities}
            allParticipants={allParticipants}
            sessions={sessions}
            onViewActivities={() => setViewMode("activities")}
            onViewStatistics={() => setViewMode("statistics")}
            onAddActivity={() => setViewMode("activities")}
            activityLevelSettings={activityLevelSettings}
            onUpdateActivityLevelSettings={setActivityLevelSettings}
          />
        )

      case "participants":
        return (
          <ParticipantManagementPage
            allParticipants={allParticipants}
            sessionParticipants={sessionParticipants}
            sessions={sessions}
            selectedSessionId={selectedSessionId}
            activities={activities}
            onAddParticipant={participantHandlers.handleAddParticipant}
            onAddSessionParticipant={participantHandlers.handleAddSessionParticipant}
            onBulkAddSessionParticipants={participantHandlers.handleBulkAddSessionParticipants}
            onBulkAddParticipants={participantHandlers.handleBulkAddParticipants}
            onUpdateParticipant={participantHandlers.handleUpdateParticipant}
            onUpdateSessionParticipant={participantHandlers.handleUpdateSessionParticipant}
            onDeleteParticipant={participantHandlers.handleDeleteParticipant}
            onRemoveFromSession={participantHandlers.handleRemoveFromSession}
            onBulkDeleteTitle={participantHandlers.handleBulkDeleteTitle}
            onBack={handleBackToMain}
            activityLevelSettings={activityLevelSettings}
          />
        )

      case "statistics":
        return (
          <StatisticsDashboard
            activities={currentSessionActivities}
            allParticipants={allParticipants}
            sessions={sessions}
            onBack={handleBackToMain}
          />
        )

      case "committees":
        return (
          <CommitteeManagement
            committees={committees}
            sessions={sessions}
            selectedSessionId={selectedSessionId}
            onAddCommittee={committeeHandlers.handleAddCommittee}
            onBulkAddCommittees={committeeHandlers.handleBulkAddCommittees}
            onEditCommittee={committeeHandlers.handleEditCommittee}
            onDeleteCommittee={committeeHandlers.handleDeleteCommittee}
            onBack={handleBackToMain}
          />
        )

      case "sessions":
        return (
          <SessionManagement
            sessions={sessions}
            onAddSession={sessionHandlers.handleAddSession}
            onEditSession={sessionHandlers.handleEditSession}
            onDeleteSession={sessionHandlers.handleDeleteSession}
            onSetActiveSession={sessionHandlers.handleSetActiveSession}
            onBack={handleBackToMain}
          />
        )

      case "test":
        return <TestStatisticsDashboard />

      case "design-test":
        return <ProfessionalDesignTest />

      case "nav-test":
        return <NavigationTest />

      case "google-sheets":
        return (
          <div className="space-y-6">
            <GoogleSheetsStatus
              connectionState={googleSheets.connectionState}
              syncStatus={googleSheets.syncStatus}
              onConnect={handleGoogleSheetsConnect}
              onDisconnect={handleGoogleSheetsDisconnect}
              onSync={handleGoogleSheetsSync}
              onToggleAutoSync={handleToggleAutoSync}
            />

            <GoogleSheetsSettings
              config={googleSheetsConfig}
              onConfigChange={setGoogleSheetsConfig}
              onTestConnection={googleSheets.testConnection}
              onAccessTokenChange={setAccessToken}
              accessToken={accessToken}
            />
          </div>
        )

      default:
        return (
          <ActivityManagement
            activities={currentSessionActivities}
            committees={currentSessionCommittees}
            sessions={sessions}
            selectedSessionId={selectedSessionId}
            onAddActivity={activityHandlers.handleAddActivity}
            onBulkAddActivitiesWithParticipants={activityHandlers.handleBulkAddActivitiesWithParticipants}
            onSelectActivity={setSelectedActivityId}
            onDeleteActivity={activityHandlers.handleDeleteActivity}
            onEditActivity={activityHandlers.handleEditActivity}
            onViewStatistics={() => setViewMode("statistics")}
            onManageCommittees={() => setViewMode("committees")}
          />
        )
    }
  }

  return (
    <div className={cn("min-h-screen bg-base-100", className)}>
      {/* Fixed Navigation */}
      <FixedNavigation
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        selectedActivity={selectedActivity}
        googleSheetsConnectionState={googleSheets.connectionState}
      />

      {/* Navigation Spacer */}
      <NavigationSpacer />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-base-100 rounded-lg shadow-elegant border border-base-300/50 overflow-hidden">
          {renderContent()}
        </div>
      </main>
    </div>
  )
}
