"use client"

import { useState, useEffect } from "react"

/**
 * Navigation Test Component
 * Tests the fixed navigation behavior across different scenarios
 */
export function NavigationTest() {
  const [scrollPosition, setScrollPosition] = useState(0)
  const [windowSize, setWindowSize] = useState({ width: 0, height: 0 })

  useEffect(() => {
    const handleScroll = () => {
      setScrollPosition(window.scrollY)
    }

    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      })
    }

    // Initial values
    handleResize()
    handleScroll()

    window.addEventListener('scroll', handleScroll)
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  const generateContent = (sections: number) => {
    return Array.from({ length: sections }, (_, i) => (
      <div key={i} className="card-professional mb-6">
        <div className="card-body">
          <h3 className="card-title">測試區塊 {i + 1}</h3>
          <p className="text-base-content text-opacity-80 mb-4">
            這是一個測試區塊，用來驗證固定導航欄的行為。當您滾動頁面時，導航欄應該保持在頂部位置。
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 6 }, (_, j) => (
              <div key={j} className="bg-base-200 p-4 rounded-lg">
                <h4 className="font-semibold mb-2">子項目 {j + 1}</h4>
                <p className="text-sm text-base-content text-opacity-70">
                  這是一些示例內容，用來填充空間並測試響應式布局。
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    ))
  }

  return (
    <div className="space-y-6">
      {/* Navigation Status Panel */}
      <div className="card-professional sticky top-20 z-30">
        <div className="card-body">
          <h2 className="card-title">固定導航測試面板</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="stat bg-base-200 rounded-lg p-4">
              <div className="stat-title">滾動位置</div>
              <div className="stat-value text-lg">{scrollPosition}px</div>
              <div className="stat-desc">
                {scrollPosition > 100 ? "已滾動" : "頂部位置"}
              </div>
            </div>
            
            <div className="stat bg-base-200 rounded-lg p-4">
              <div className="stat-title">視窗大小</div>
              <div className="stat-value text-lg">{windowSize.width}×{windowSize.height}</div>
              <div className="stat-desc">
                {windowSize.width < 640 ? "手機" : 
                 windowSize.width < 1024 ? "平板" : "桌面"}
              </div>
            </div>

            <div className="stat bg-base-200 rounded-lg p-4">
              <div className="stat-title">導航狀態</div>
              <div className="stat-value text-lg">
                <span className="text-success">✓</span>
              </div>
              <div className="stat-desc">固定位置正常</div>
            </div>
          </div>
        </div>
      </div>

      {/* Test Instructions */}
      <div className="alert alert-info">
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <div>
          <h3 className="font-bold">測試說明</h3>
          <div className="text-sm mt-1">
            <p>• 滾動頁面測試導航欄是否保持固定位置</p>
            <p>• 調整瀏覽器窗口大小測試響應式行為</p>
            <p>• 切換不同頁面標籤測試導航一致性</p>
            <p>• 測試鍵盤導航和無障礙功能</p>
          </div>
        </div>
      </div>

      {/* Responsive Test Grid */}
      <div className="card-professional">
        <div className="card-body">
          <h3 className="card-title">響應式測試網格</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            {Array.from({ length: 12 }, (_, i) => (
              <div key={i} className="bg-gradient-to-br from-primary to-secondary text-primary-content p-4 rounded-lg text-center">
                <div className="text-2xl font-bold">{i + 1}</div>
                <div className="text-xs opacity-80">響應式卡片</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Long Content for Scroll Testing */}
      <div className="space-y-6">
        <h3 className="text-xl font-bold text-base-content">滾動測試內容</h3>
        {generateContent(10)}
      </div>

      {/* Z-index Test */}
      <div className="card-professional">
        <div className="card-body">
          <h3 className="card-title">Z-index 層級測試</h3>
          <p className="text-base-content text-opacity-80 mb-4">
            測試導航欄與其他元素的層級關係
          </p>
          
          <div className="flex gap-4 flex-wrap">
            <button 
              className="btn-professional btn-primary"
              onClick={() => {
                const modal = document.getElementById('test-modal') as HTMLDialogElement
                modal?.showModal()
              }}
            >
              測試模態框層級
            </button>
            
            <div className="dropdown">
              <div tabIndex={0} role="button" className="btn-professional btn-secondary">
                測試下拉選單層級
              </div>
              <ul tabIndex={0} className="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow-elegant border border-base-300">
                <li><a>選項 1</a></li>
                <li><a>選項 2</a></li>
                <li><a>選項 3</a></li>
              </ul>
            </div>

            <div className="tooltip tooltip-bottom" data-tip="這是一個工具提示">
              <button className="btn-professional btn-accent">工具提示測試</button>
            </div>
          </div>
        </div>
      </div>

      {/* Test Modal */}
      <dialog id="test-modal" className="modal modal-professional">
        <div className="modal-box">
          <h3 className="text-lg font-bold mb-4">模態框層級測試</h3>
          <p className="mb-4">
            這個模態框應該顯示在固定導航欄的上方。Z-index 層級應該正確設置。
          </p>
          <div className="modal-action">
            <form method="dialog">
              <button className="btn-professional btn-ghost">關閉</button>
            </form>
          </div>
        </div>
        <form method="dialog" className="modal-backdrop">
          <button>close</button>
        </form>
      </dialog>

      {/* Accessibility Test */}
      <div className="card-professional">
        <div className="card-body">
          <h3 className="card-title">無障礙功能測試</h3>
          <p className="text-base-content text-opacity-80 mb-4">
            使用 Tab 鍵測試鍵盤導航，確保固定導航不會干擾焦點順序
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="label">
                <span className="label-text">測試輸入框 1</span>
              </label>
              <input type="text" className="input-professional" placeholder="使用 Tab 鍵導航" />
            </div>
            
            <div className="space-y-2">
              <label className="label">
                <span className="label-text">測試輸入框 2</span>
              </label>
              <input type="text" className="input-professional" placeholder="測試焦點順序" />
            </div>
            
            <div className="space-y-2">
              <label className="label">
                <span className="label-text">測試選擇框</span>
              </label>
              <select className="select-professional">
                <option>選項 1</option>
                <option>選項 2</option>
                <option>選項 3</option>
              </select>
            </div>
            
            <div className="space-y-2">
              <label className="label">
                <span className="label-text">測試文本區域</span>
              </label>
              <textarea className="textarea-professional" placeholder="測試多行輸入"></textarea>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Test */}
      <div className="card-professional">
        <div className="card-body">
          <h3 className="card-title">性能測試</h3>
          <p className="text-base-content text-opacity-80 mb-4">
            快速滾動測試固定導航的性能表現
          </p>
          
          <button 
            className="btn-professional btn-primary"
            onClick={() => {
              window.scrollTo({ top: 0, behavior: 'smooth' })
            }}
          >
            平滑滾動到頂部
          </button>
        </div>
      </div>

      {/* Bottom Spacer */}
      <div className="h-96 flex items-center justify-center bg-base-200 rounded-lg">
        <div className="text-center">
          <h4 className="text-xl font-bold text-base-content mb-2">測試完成</h4>
          <p className="text-base-content text-opacity-70">
            您已經滾動到頁面底部，導航欄應該仍然可見
          </p>
        </div>
      </div>
    </div>
  )
}
